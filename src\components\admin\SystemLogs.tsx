'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  AlertCircle,
  RefreshCw,
  Info,
  Bug,
  Server,
  Shield,
  Cpu,
  Database,
} from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { formatDistanceToNow } from 'date-fns';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToastHelpers } from '@/lib/ToastContext';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { useAuth } from '@/contexts/AuthContext';

interface SystemLog {
  _id: string;
  level: 'info' | 'warning' | 'error' | 'debug';
  message: string;
  source: string;
  details?: string;
  timestamp: string;
}

interface SystemLogsProps {
  limit?: number;
  autoRefresh?: boolean;
  refreshInterval?: number;
  height?: string;
}

export default function SystemLogs({
  limit = 20,
  autoRefresh: initialAutoRefresh = false,
  refreshInterval: initialRefreshInterval = 60000,
  height = "500px"
}: SystemLogsProps) {
  const [logs, setLogs] = useState<SystemLog[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [logLevel, setLogLevel] = useState<string>('all');
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());
  const [autoRefresh, setAutoRefresh] = useState(initialAutoRefresh);
  const [refreshInterval, setRefreshInterval] = useState(initialRefreshInterval);

  const { user, isAdmin } = useAuth();
  const toast = useToastHelpers();
  const retryCount = useRef(0);
  const maxRetries = 2;
  const toastShown = useRef(false);
  const fetchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const autoRefreshTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isFetching = useRef(false);
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Fetch system logs with debouncing and spam prevention
  const fetchLogs = useCallback(async (silent = false) => {
    // Prevent multiple simultaneous requests
    if (isFetching.current) {
      return;
    }

    // Clear any existing debounce timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // Debounce the request
    debounceTimeoutRef.current = setTimeout(async () => {
      isFetching.current = true;

      if (!silent) {
        setIsLoading(true);
      }
      setError(null);

    try {
      if (!user || !user.id) {
        throw new Error('Authentication required. Please sign in again.');
      }

      if (!isAdmin()) {
        throw new Error('You do not have permission to view system logs.');
      }

      const url = new URL('/api/admin/system/logs', window.location.origin);
      url.searchParams.append('limit', limit.toString());
      url.searchParams.append('userId', user.id);

      if (logLevel !== 'all') {
        url.searchParams.append('level', logLevel);
      }

      if (!autoRefresh) {
        url.searchParams.append('_t', Date.now().toString());
      }

      const response = await fetch(url.toString(), {
        credentials: 'include',
        cache: 'no-store'
      });

      if (!response.ok) {
        const errorData = await response.json();
        if (response.status === 401) {
          throw new Error('Authentication required. Please sign in again.');
        } else if (response.status === 403) {
          throw new Error('You do not have permission to view system logs.');
        }
        throw new Error(errorData.error || `Failed to fetch logs (${response.status})`);
      }

      const data = await response.json();
      setLogs(data.logs || []);
      setLastRefresh(new Date());
      retryCount.current = 0;
      toastShown.current = false;
    } catch (error) {
      console.error('Error fetching system logs:', error);
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      setError(errorMessage);

      if (!toastShown.current && !silent && retryCount.current >= maxRetries) {
        toast.error('Error', 'Failed to fetch system logs');
        toastShown.current = true;
      }

      if (retryCount.current < maxRetries) {
        const delay = Math.pow(2, retryCount.current) * 1000;
        retryCount.current++;
        fetchTimeoutRef.current = setTimeout(() => {
          fetchLogs(true);
        }, delay);
      }
    } finally {
      setIsLoading(false);
      isFetching.current = false;
    }
    }, 300); // 300ms debounce delay
  }, [limit, logLevel, toast, autoRefresh, user, isAdmin]);

  // Auto-refresh effect
  useEffect(() => {
    if (autoRefreshTimeoutRef.current) {
      clearTimeout(autoRefreshTimeoutRef.current);
      autoRefreshTimeoutRef.current = null;
    }

    if (autoRefresh && refreshInterval > 0) {
      autoRefreshTimeoutRef.current = setTimeout(() => {
        fetchLogs(true);
      }, refreshInterval);
    }

    return () => {
      if (autoRefreshTimeoutRef.current) {
        clearTimeout(autoRefreshTimeoutRef.current);
      }
    };
  }, [autoRefresh, refreshInterval]);

  // Initial fetch - only run once on mount
  useEffect(() => {
    fetchLogs();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Handle filter changes
  useEffect(() => {
    if (logs.length > 0) { // Only refetch if we already have data
      fetchLogs();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [logLevel, limit]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (fetchTimeoutRef.current) {
        clearTimeout(fetchTimeoutRef.current);
      }
      if (autoRefreshTimeoutRef.current) {
        clearTimeout(autoRefreshTimeoutRef.current);
      }
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
      isFetching.current = false;
    };
  }, []);

  // Helper functions
  const formatRelativeTime = (timestamp: string) => {
    try {
      return formatDistanceToNow(new Date(timestamp), { addSuffix: true });
    } catch (e) {
      return 'Unknown time';
    }
  };

  const getLogIcon = (level: string) => {
    switch (level) {
      case 'error':
        return <AlertCircle className="h-4 w-4" />;
      case 'warning':
        return <AlertCircle className="h-4 w-4" />;
      case 'info':
        return <Info className="h-4 w-4" />;
      case 'debug':
        return <Bug className="h-4 w-4" />;
      default:
        return <Info className="h-4 w-4" />;
    }
  };

  const getSourceIcon = (source: string) => {
    switch (source.toLowerCase()) {
      case 'server':
        return <Server className="h-4 w-4" />;
      case 'auth':
        return <Shield className="h-4 w-4" />;
      case 'system':
        return <Cpu className="h-4 w-4" />;
      case 'database':
        return <Database className="h-4 w-4" />;
      default:
        return <Server className="h-4 w-4" />;
    }
  };

  const getLogLevelVariant = (level: string): "default" | "secondary" | "destructive" | "outline" => {
    switch (level) {
      case 'error':
        return 'destructive';
      case 'warning':
        return 'default';
      case 'info':
        return 'secondary';
      case 'debug':
        return 'outline';
      default:
        return 'outline';
    }
  };

  const displayLogs = logs.length > 0 ? logs : [];

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-3 flex-wrap gap-2">
        <div>
          <CardTitle className="text-vista-light">System Logs</CardTitle>
          <CardDescription>
            Server and system-level events
          </CardDescription>
        </div>
        <div className="flex items-center gap-4">
          <div className="flex items-center space-x-2">
            <Switch
              id="auto-refresh"
              checked={autoRefresh}
              onCheckedChange={setAutoRefresh}
            />
            <Label htmlFor="auto-refresh" className="text-sm">Auto-refresh</Label>
          </div>
          <Select value={logLevel} onValueChange={setLogLevel}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Level" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All</SelectItem>
              <SelectItem value="error">Error</SelectItem>
              <SelectItem value="warning">Warning</SelectItem>
              <SelectItem value="info">Info</SelectItem>
              <SelectItem value="debug">Debug</SelectItem>
            </SelectContent>
          </Select>
          <Button
            variant="outline"
            size="sm"
            onClick={() => fetchLogs()}
            disabled={isLoading}
            className="transition-all duration-200 hover:bg-vista-blue/10 hover:text-vista-blue hover:border-vista-blue/30 active:scale-95"
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        {error && !isLoading ? (
          <div className="p-6 text-center">
            <AlertCircle className="h-8 w-8 text-red-400 mx-auto mb-2" />
            <p className="text-red-400 mb-4">{error}</p>
            <Button
              variant="outline"
              onClick={() => fetchLogs()}
              className="transition-all duration-200 hover:bg-vista-blue/10 hover:text-vista-blue hover:border-vista-blue/30 active:scale-95"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Try Again
            </Button>
          </div>
        ) : (
          <div className="relative border rounded-md overflow-hidden">
            {/* Mobile-optimized layout */}
            <div className="block md:hidden">
              <div
                className="admin-scroll-mobile overflow-auto"
                style={{ height, overflowX: "hidden" }}
              >
                <div className="admin-mobile-container">
                  {isLoading ? (
                    Array.from({ length: 5 }).map((_, index) => (
                      <div key={index} className="admin-card-mobile">
                        <Skeleton className="h-4 w-20 mb-2" />
                        <Skeleton className="h-4 w-full mb-1" />
                        <Skeleton className="h-3 w-3/4" />
                      </div>
                    ))
                  ) : displayLogs.length > 0 ? (
                    displayLogs.map((log) => (
                      <div
                        key={log._id}
                        className={`admin-card-mobile ${log.level === 'error' ? 'border-red-500/30 bg-red-900/20' : ''}`}
                      >
                        <div className="flex items-start justify-between mb-3">
                          <Badge
                            variant={getLogLevelVariant(log.level)}
                            className="flex items-center gap-1 text-xs px-2 py-1"
                          >
                            {getLogIcon(log.level)}
                            {log.level}
                          </Badge>
                          <span className="text-xs text-vista-light/60 font-mono">
                            {formatRelativeTime(log.timestamp)}
                          </span>
                        </div>
                        <div className="flex items-center gap-2 mb-3">
                          <div className="flex-shrink-0">
                            {getSourceIcon(log.source)}
                          </div>
                          <span className="text-sm font-medium text-vista-light truncate">{log.source}</span>
                        </div>
                        <p className="text-sm text-vista-light/90 break-words leading-relaxed mb-2">{log.message}</p>
                        {log.details && (
                          <details className="mt-3">
                            <summary className="text-xs text-vista-blue cursor-pointer hover:text-vista-blue/80 select-none">
                              View Details
                            </summary>
                            <pre className="text-xs text-vista-light/70 mt-2 whitespace-pre-wrap break-words bg-vista-dark/50 p-3 rounded-lg border border-vista-light/10">
                              {log.details}
                            </pre>
                          </details>
                        )}
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-12 text-vista-light/50">
                      <AlertCircle className="h-12 w-12 mx-auto mb-3 opacity-50" />
                      <p className="text-base">No logs available</p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Desktop table layout */}
            <div className="hidden md:block">
              <div
                className="custom-scrollbar overflow-auto"
                style={{ height, overflowX: "auto" }}
              >
                <table className="w-full border-collapse">
                  <thead className="sticky top-0 bg-background z-10">
                    <tr>
                      <th className="w-36 text-vista-light/70 text-left p-3 border-b border-vista-light/10">Time</th>
                      <th className="w-24 text-vista-light/70 text-left p-3 border-b border-vista-light/10">Level</th>
                      <th className="w-32 text-vista-light/70 text-left p-3 border-b border-vista-light/10">Source</th>
                      <th className="text-vista-light/70 text-left p-3 border-b border-vista-light/10">Message</th>
                      <th className="w-20 text-vista-light/70 text-left p-3 border-b border-vista-light/10">Details</th>
                    </tr>
                  </thead>
                  <tbody>
                    {isLoading ? (
                      Array.from({ length: 5 }).map((_, index) => (
                        <tr key={index}>
                          <td className="p-3 border-b border-vista-light/5">
                            <Skeleton className="h-4 w-24" />
                          </td>
                          <td className="p-3 border-b border-vista-light/5">
                            <Skeleton className="h-6 w-16" />
                          </td>
                          <td className="p-3 border-b border-vista-light/5">
                            <Skeleton className="h-4 w-20" />
                          </td>
                          <td className="p-3 border-b border-vista-light/5">
                            <Skeleton className="h-4 w-full" />
                          </td>
                          <td className="p-3 border-b border-vista-light/5">
                            <Skeleton className="h-4 w-12" />
                          </td>
                        </tr>
                      ))
                    ) : displayLogs.length > 0 ? (
                      displayLogs.map((log) => (
                        <tr key={log._id} className={`hover:bg-vista-light/5 ${log.level === 'error' ? 'bg-red-900/10' : ''}`}>
                          <td className="p-3 border-b border-vista-light/5 text-sm text-vista-light/70">
                            {formatRelativeTime(log.timestamp)}
                          </td>
                          <td className="p-3 border-b border-vista-light/5">
                            <Badge
                              variant={getLogLevelVariant(log.level)}
                              className="flex items-center gap-1 w-fit"
                            >
                              {getLogIcon(log.level)}
                              {log.level}
                            </Badge>
                          </td>
                          <td className="p-3 border-b border-vista-light/5">
                            <div className="flex items-center gap-2">
                              {getSourceIcon(log.source)}
                              <span className="text-sm text-vista-light">{log.source}</span>
                            </div>
                          </td>
                          <td className="p-3 border-b border-vista-light/5 text-sm text-vista-light/90 max-w-md">
                            <div className="truncate" title={log.message}>
                              {log.message}
                            </div>
                          </td>
                          <td className="p-3 border-b border-vista-light/5 text-center">
                            {log.details && (
                              <details className="inline">
                                <summary className="text-xs text-vista-blue cursor-pointer hover:text-vista-blue/80">
                                  View
                                </summary>
                                <div className="absolute z-50 mt-1 p-2 bg-vista-dark border border-vista-light/20 rounded shadow-lg max-w-sm">
                                  <pre className="text-xs text-vista-light/70 whitespace-pre-wrap break-words">
                                    {log.details}
                                  </pre>
                                </div>
                              </details>
                            )}
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={5} className="text-center py-8 text-vista-light/50">
                          <AlertCircle className="h-8 w-8 mx-auto mb-2 opacity-50" />
                          <p>No logs available</p>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}
        <div className="text-xs text-vista-light/50 mt-2 text-right p-4">
          Last updated: {lastRefresh.toLocaleTimeString()}
          {autoRefresh && (
            <span className="ml-2">
              (Auto-refresh {Math.round(refreshInterval / 1000)}s)
            </span>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
