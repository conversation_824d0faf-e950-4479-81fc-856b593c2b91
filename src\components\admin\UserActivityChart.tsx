'use client';

import React, { useMemo } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Users, Clock, Calendar } from 'lucide-react';
import {
  BarChart as RechartsBarChart, // Rename to avoid conflict
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';

// Simplified activity data interface
interface ActivityData {
  type: string;
  action: string;
  message: string;
  details: string;
  timestamp: string;
  userId?: string;
}

interface UserActivityChartProps {
  data: ActivityData[];
}

export function UserActivityChart({ data = [] }: UserActivityChartProps) {
  // Process activity data for the chart (simplified)
  const chartData = useMemo(() => {
    if (!data || data.length === 0) {
      console.log('No activity data provided to UserActivityChart');
      return [];
    }

    console.log(`Processing ${data.length} activity items for chart`);

    // Get the last 7 days
    const last7Days = Array.from({ length: 7 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - (6 - i));
      return date.toISOString().split('T')[0]; // Format as YYYY-MM-DD
    });

    // Initialize with zero counts for all days
    const dateMap = last7Days.reduce((acc, date) => {
      acc[date] = {
        date,
        logins: 0,
        signups: 0
      };
      return acc;
    }, {} as Record<string, { date: string; logins: number; signups: number }>);

    // Process the activity data
    data.forEach(item => {
      try {
        const itemDate = new Date(item.timestamp);
        if (isNaN(itemDate.getTime())) return;

        const dateStr = itemDate.toISOString().split('T')[0];

        // Only count activities from the last 7 days
        if (dateMap[dateStr]) {
          if (item.type === 'user_login' || (item.type === 'auth' && item.action === 'login')) {
            dateMap[dateStr].logins += 1;
          } else if (item.type === 'user_registration' || (item.type === 'auth' && item.action === 'signup')) {
            dateMap[dateStr].signups += 1;
          }
        }
      } catch (error) {
        console.error('Error processing activity item:', error);
      }
    });

    return Object.values(dateMap);
  }, [data]);

  // Count total metrics
  const loginCount = useMemo(() => {
    return chartData.reduce((sum, day) => sum + day.logins, 0);
  }, [chartData]);

  const signupCount = useMemo(() => {
    return chartData.reduce((sum, day) => sum + day.signups, 0);
  }, [chartData]);



  // Format dates for display
  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };

  return (
    <CardContent className="p-2 sm:p-3 md:p-6">
      <div className="space-y-3 sm:space-y-4">
        {/* Activity chart visualization - Mobile optimized */}
        <div className="h-40 sm:h-48 md:h-64 rounded-md">
          {chartData.length > 0 && (loginCount > 0 || signupCount > 0) ? (
            <ResponsiveContainer width="100%" height="100%">
              <RechartsBarChart
                data={chartData}
                margin={{
                  top: 5,
                  right: 5,
                  left: -20, // Adjust to prevent Y-axis label cutoff on mobile
                  bottom: 5,
                }}
                barGap={2}
              >
                <defs>
                  <linearGradient id="loginGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8} />
                    <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.1} />
                  </linearGradient>
                  <linearGradient id="signupGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#10b981" stopOpacity={0.8} />
                    <stop offset="95%" stopColor="#10b981" stopOpacity={0.1} />
                  </linearGradient>
                </defs>
                <CartesianGrid
                  strokeDasharray="3 3"
                  stroke="#374151"
                  opacity={0.3}
                  horizontal={true}
                  vertical={false}
                />
                <XAxis
                  dataKey="date"
                  tickFormatter={formatDate}
                  tick={{ fill: '#9CA3AF', fontSize: 9 }}
                  tickLine={false}
                  axisLine={{ stroke: "#4B5563", strokeWidth: 1 }}
                  interval="preserveStartEnd"
                  minTickGap={15}
                />
                <YAxis
                  tick={{ fill: '#9CA3AF', fontSize: 9 }}
                  tickLine={false}
                  axisLine={{ stroke: "#4B5563", strokeWidth: 1 }}
                  allowDecimals={false}
                  width={25}
                />
                <Tooltip
                  cursor={{ fill: 'rgba(59, 130, 246, 0.1)', radius: 4 }}
                  content={({ active, payload, label }) => {
                    if (active && payload && payload.length) {
                      return (
                        <div className="bg-vista-dark/95 backdrop-blur-sm border border-vista-light/20 rounded-lg p-2 sm:p-3 shadow-xl max-w-[200px] sm:max-w-none">
                          <p className="text-vista-light font-medium mb-1 sm:mb-2 text-xs sm:text-sm">{formatDate(label)}</p>
                          {payload.map((entry: any, index: number) => (
                            <p key={index} className="text-xs sm:text-sm flex items-center gap-1 sm:gap-2">
                              <span
                                className="w-2 h-2 sm:w-3 sm:h-3 rounded-sm flex-shrink-0"
                                style={{ backgroundColor: entry.color }}
                              />
                              <span className="truncate">{`${entry.dataKey}: ${entry.value}`}</span>
                            </p>
                          ))}
                        </div>
                      );
                    }
                    return null;
                  }}
                />
                <Legend
                  iconSize={12}
                  wrapperStyle={{
                    fontSize: '12px',
                    paddingTop: '15px',
                    color: '#9CA3AF'
                  }}
                />
                <Bar
                  dataKey="logins"
                  fill="url(#loginGradient)"
                  name="Logins"
                  radius={[3, 3, 0, 0]}
                  animationDuration={1200}
                  animationBegin={0}
                />
                <Bar
                  dataKey="signups"
                  fill="url(#signupGradient)"
                  name="Signups"
                  radius={[3, 3, 0, 0]}
                  animationDuration={1200}
                  animationBegin={200}
                />
              </RechartsBarChart>
            </ResponsiveContainer>
          ) : (
            <div className="h-full flex items-center justify-center bg-slate-800 rounded-md">
              <div className="text-center">
                <RechartsBarChart className="h-12 w-12 mx-auto text-blue-400 opacity-70 mb-2" />
                <p className="text-slate-300">No user activity data available for the last 7 days</p>
                <p className="text-slate-400 text-sm mt-1">User logins and signups will appear here when they occur</p>
              </div>
            </div>
          )}
        </div>

        {/* Enhanced Summary stats */}
        <div className="grid grid-cols-2 gap-4 mt-6">
          <div className="bg-gradient-to-br from-blue-500/10 to-blue-600/5 border border-blue-500/20 p-4 rounded-lg backdrop-blur-sm">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <div className="p-2 bg-blue-500/20 rounded-lg mr-3">
                  <Users className="h-4 w-4 text-blue-400" />
                </div>
                <p className="text-sm text-vista-light/80 font-medium">Logins</p>
              </div>
              <div className="text-xs text-blue-400 bg-blue-500/10 px-2 py-1 rounded-full">
                7 days
              </div>
            </div>
            {loginCount > 0 ? (
              <>
                <p className="text-2xl font-bold text-white mb-1">{loginCount.toLocaleString()}</p>
                <p className="text-xs text-blue-400">
                  User sessions
                </p>
              </>
            ) : (
              <div className="py-2">
                <p className="text-lg text-vista-light/50 mb-1">0</p>
                <p className="text-xs text-vista-light/40">No recent logins</p>
              </div>
            )}
          </div>
          <div className="bg-gradient-to-br from-emerald-500/10 to-emerald-600/5 border border-emerald-500/20 p-4 rounded-lg backdrop-blur-sm">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <div className="p-2 bg-emerald-500/20 rounded-lg mr-3">
                  <Calendar className="h-4 w-4 text-emerald-400" />
                </div>
                <p className="text-sm text-vista-light/80 font-medium">Signups</p>
              </div>
              <div className="text-xs text-emerald-400 bg-emerald-500/10 px-2 py-1 rounded-full">
                7 days
              </div>
            </div>
            {signupCount > 0 ? (
              <>
                <p className="text-2xl font-bold text-white mb-1">{signupCount.toLocaleString()}</p>
                <p className="text-xs text-emerald-400">
                  New users joined
                </p>
              </>
            ) : (
              <div className="py-2">
                <p className="text-lg text-vista-light/50 mb-1">0</p>
                <p className="text-xs text-vista-light/40">No new signups</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </CardContent>
  );
}
